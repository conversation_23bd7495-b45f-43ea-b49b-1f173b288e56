// src/utils/transformChartData.ts

import { CandlestickData, Time } from 'lightweight-charts';

/**
 * Transforms Alpaca API response data into chart-compatible format
 *
 * Alpaca API returns data in this format:
 * {
 *   "bars": {
 *     "SYMBOL": [
 *       {
 *         "t": "2023-01-01T09:30:00Z", // timestamp
 *         "o": 100.0,                  // open
 *         "h": 101.0,                  // high
 *         "l": 99.0,                   // low
 *         "c": 100.5,                  // close
 *         "v": 1000                    // volume
 *       }
 *     ]
 *   }
 * }
 */

export interface AlpacaBar {
  t: string;  // timestamp
  o: number;  // open
  h: number;  // high
  l: number;  // low
  c: number;  // close
  v: number;  // volume
}

export interface AlpacaResponse {
  bars: {
    [symbol: string]: AlpacaBar[];
  };
}

export interface ChartDataPoint extends CandlestickData<Time> {
  volume: number;
}

/**
 * Transforms Alpaca API response into chart-compatible data
 * @param alpacaData - Raw response from Alpaca API
 * @returns Array of chart data points
 */
export function transformAlpacaDataToChart(alpacaData: AlpacaResponse): ChartDataPoint[] {
  if (!alpacaData || !alpacaData.bars) {
    console.warn('No bars data found in Alpaca response');
    return [];
  }

  // Get the first symbol's data (there should only be one symbol in the response)
  const symbols = Object.keys(alpacaData.bars);
  if (symbols.length === 0) {
    console.warn('No symbols found in Alpaca response');
    return [];
  }

  const symbolData = alpacaData.bars[symbols[0]];
  if (!Array.isArray(symbolData)) {
    console.warn('Symbol data is not an array');
    return [];
  }

  // Transform each bar into chart format
  return symbolData.map((bar: AlpacaBar): ChartDataPoint => {
    // Convert ISO timestamp to Date object
    // Alpaca returns timestamps in Eastern Time (e.g., "2025-07-22T09:30:00-04:00")
    const date = new Date(bar.t);

    // Convert to Unix timestamp (seconds since epoch)
    // This preserves the correct Eastern Time
    const timestamp = Math.floor(date.getTime() / 1000) as Time;

    return {
      time: timestamp,
      open: bar.o,
      high: bar.h,
      low: bar.l,
      close: bar.c,
      volume: bar.v
    };
  });
}

/**
 * Validates that the data contains the required fields for charting
 * @param data - Chart data to validate
 * @returns true if data is valid, false otherwise
 */
export function validateChartData(data: ChartDataPoint[]): boolean {
  if (!Array.isArray(data) || data.length === 0) {
    return false;
  }

  return data.every(point =>
    point.time !== null &&
    point.time !== undefined &&
    typeof point.open === 'number' &&
    typeof point.high === 'number' &&
    typeof point.low === 'number' &&
    typeof point.close === 'number' &&
    typeof point.volume === 'number' &&
    !isNaN(point.open) &&
    !isNaN(point.high) &&
    !isNaN(point.low) &&
    !isNaN(point.close) &&
    !isNaN(point.volume)
  );
}
