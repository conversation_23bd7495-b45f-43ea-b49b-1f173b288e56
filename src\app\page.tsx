// src/app/page.tsx

'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { formatAlpacaOptionSymbol } from '../utils/formatAlpacaOptionSymbol';
import { fetchHistoricalOptionBars } from '@/utils/fetchHistoricalOptionBars';
import { fetchHistoricalStockBars } from '@/utils/fetchHistoricalStockBars';
import { transformAlpacaDataToChart, validateChartData } from '@/utils/transformChartData';
import TradingViewChart, { TradingViewChartRef } from '@/components/TradingViewChart';
import { Time } from 'lightweight-charts';
import AutosuggestInput from '@/components/AutosuggestInput';
import tickersData from '@/data/tickers.json';

export default function HomePage() {
  // Form toggle state
  const [formType, setFormType] = useState<'options' | 'stocks'>('options');

  // Options form state
  const [symbol, setSymbol] = useState('');
  const [expiration, setExpiration] = useState('');
  const [type, setType] = useState<'C' | 'P'>('C');
  const [strike, setStrike] = useState('');
  const [timeframe, setTimeframe] = useState('1Min');
  const [startDate, setStartDate] = useState(() => {
    // Default to current date
    const today = new Date();
    return today.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState(() => {
    // Default to current date (will be updated when expiration is set)
    const today = new Date();
    return today.toISOString().split('T')[0];
  });
  const [hasUserChangedDateRange, setHasUserChangedDateRange] = useState(false);

  // Stock form state
  const [stockSymbol, setStockSymbol] = useState('');
  const [stockDate, setStockDate] = useState(() => {
    // Default to current date
    const today = new Date();
    return today.toISOString().split('T')[0];
  });

  // Common state
  const [isFormMinimized, setIsFormMinimized] = useState(false);
  const [hasDataBeenFetched, setHasDataBeenFetched] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const chartRef = useRef<TradingViewChartRef>(null);

  // Risk Management Scenario type
  interface RiskScenario {
    id: string;
    takeProfitOffset: number;
    takeProfitAchieved: boolean;
    takeProfitTimestamp?: string; // Timestamp when take profit was achieved
    slippageResults?: { // Results for each slippage scenario
      slippageId: string;
      slippageAmount: number;
      takeProfitAchieved: boolean;
      takeProfitTimestamp?: string;
    }[];
  }

  // Straddle analysis results state (2nd candle entry)
  const [optionAnalysisResults, setOptionAnalysisResults] = useState<{
    strikePrice: number;
    callPurchasePrice: number;
    putPurchasePrice: number;
    straddlePurchasePrice: number;
    breakoutBarTime: string;
    callMFE: number;
    putMFE: number;
    straddleMFE: number;
    callClosePrice: number;
    putClosePrice: number;
    straddleClosePrice: number;
    riskScenarios: RiskScenario[];
  } | null>(null);

  // Straddle analysis results state (4th candle entry)
  const [optionAnalysisResults4th, setOptionAnalysisResults4th] = useState<{
    strikePrice: number;
    callPurchasePrice: number;
    putPurchasePrice: number;
    straddlePurchasePrice: number;
    breakoutBarTime: string;
    callMFE: number;
    putMFE: number;
    straddleMFE: number;
    callClosePrice: number;
    putClosePrice: number;
    straddleClosePrice: number;
    riskScenarios: RiskScenario[];
  } | null>(null);

  // Straddle analysis results state (7th candle entry)
  const [optionAnalysisResults7th, setOptionAnalysisResults7th] = useState<{
    strikePrice: number;
    callPurchasePrice: number;
    putPurchasePrice: number;
    straddlePurchasePrice: number;
    breakoutBarTime: string;
    callMFE: number;
    putMFE: number;
    straddleMFE: number;
    callClosePrice: number;
    putClosePrice: number;
    straddleClosePrice: number;
    riskScenarios: RiskScenario[];
  } | null>(null);

  // Straddle analysis results state (13th candle entry)
  const [optionAnalysisResults13th, setOptionAnalysisResults13th] = useState<{
    strikePrice: number;
    callPurchasePrice: number;
    putPurchasePrice: number;
    straddlePurchasePrice: number;
    breakoutBarTime: string;
    callMFE: number;
    putMFE: number;
    straddleMFE: number;
    callClosePrice: number;
    putClosePrice: number;
    straddleClosePrice: number;
    riskScenarios: RiskScenario[];
  } | null>(null);

  // Straddle analysis results state (1st candle entry - for breakeven lines)
  const [optionAnalysisResultsFirstCandle, setOptionAnalysisResultsFirstCandle] = useState<{
    strikePrice: number;
    callPurchasePrice: number;
    putPurchasePrice: number;
    straddlePurchasePrice: number;
    breakoutBarTime: string;
    callMFE: number;
    putMFE: number;
    straddleMFE: number;
    callClosePrice: number;
    putClosePrice: number;
    straddleClosePrice: number;
    riskScenarios: RiskScenario[];
  } | null>(null);



  // Dynamic risk scenarios array
  const [riskScenarios, setRiskScenarios] = useState<RiskScenario[]>([
    { id: '1', takeProfitOffset: 0.25, takeProfitAchieved: false },
    { id: '2', takeProfitOffset: 0.50, takeProfitAchieved: false },
    { id: '3', takeProfitOffset: 0.75, takeProfitAchieved: false },
    { id: '4', takeProfitOffset: 1.00, takeProfitAchieved: false }
  ]);

  // Slippage scenarios state
  const [slippageScenarios, setSlippageScenarios] = useState<{ id: string; slippageAmount: number }[]>([
    { id: '1', slippageAmount: 0.00 }
  ]);



  // Functions to manage risk scenarios
  const addRiskScenario = () => {
    const newId = (riskScenarios.length + 1).toString();
    const newScenario: RiskScenario = {
      id: newId,
      takeProfitOffset: 0.25,
      takeProfitAchieved: false
    };
    setRiskScenarios([...riskScenarios, newScenario]);
  };

  const removeRiskScenario = (id: string) => {
    if (riskScenarios.length > 1) { // Keep at least one scenario
      setRiskScenarios(riskScenarios.filter(scenario => scenario.id !== id));
    }
  };

  const updateRiskScenario = (id: string, field: 'takeProfitOffset', value: number) => {
    setRiskScenarios(riskScenarios.map(scenario =>
      scenario.id === id ? { ...scenario, [field]: value } : scenario
    ));
  };

  // Functions to manage slippage scenarios
  const addSlippageScenario = () => {
    const newId = (slippageScenarios.length + 1).toString();
    const newScenario = {
      id: newId,
      slippageAmount: 0.00
    };
    setSlippageScenarios([...slippageScenarios, newScenario]);
  };

  const removeSlippageScenario = (id: string) => {
    if (slippageScenarios.length > 1) { // Keep at least one scenario
      setSlippageScenarios(slippageScenarios.filter(scenario => scenario.id !== id));
    }
  };

  const updateSlippageScenario = (id: string, value: number) => {
    setSlippageScenarios(slippageScenarios.map(scenario =>
      scenario.id === id ? { ...scenario, slippageAmount: value } : scenario
    ));
  };

  const formatted = formatAlpacaOptionSymbol(
    symbol,
    expiration,
    type,
    parseFloat(strike)
  );

  // Auto-update both start and end dates when expiration changes (unless user has manually changed them)
  useEffect(() => {
    if (expiration && !hasUserChangedDateRange) {
      setStartDate(expiration);
      setEndDate(expiration);
    }
  }, [expiration, hasUserChangedDateRange]);

  // Trigger chart resize when form minimize state changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (chartRef.current) {
        chartRef.current.resize();
      }
    }, 350); // Wait for CSS transition to complete

    return () => clearTimeout(timer);
  }, [isFormMinimized]);

  // Log when first candle straddle results are available (for breakeven lines)
  useEffect(() => {
    if (optionAnalysisResultsFirstCandle) {
      console.log('First candle straddle analysis completed for breakeven lines:', {
        strikePrice: optionAnalysisResultsFirstCandle.strikePrice,
        straddlePurchasePrice: optionAnalysisResultsFirstCandle.straddlePurchasePrice,
        breakevenHigh: optionAnalysisResultsFirstCandle.strikePrice + optionAnalysisResultsFirstCandle.straddlePurchasePrice,
        breakevenLow: optionAnalysisResultsFirstCandle.strikePrice - optionAnalysisResultsFirstCandle.straddlePurchasePrice
      });
    }
  }, [optionAnalysisResultsFirstCandle]);

  // Calculate option metrics (MFE, close price, and take profit achieved for dynamic scenarios)
  const calculateOptionMetrics = useCallback((
    optionChartData: unknown[],
    matchingBar: unknown,
    breakoutTime: unknown,
    scenarios: RiskScenario[]
  ): { mfe: number; closePrice: number; riskResults: RiskScenario[] } => {
    // Type guards for data validation
    const isValidBar = (bar: unknown): bar is { time: number; high: number; low: number; close: number } => {
      return typeof bar === 'object' && bar !== null &&
             'time' in bar && 'high' in bar && 'low' in bar && 'close' in bar;
    };

    const isValidMatchingBar = (bar: unknown): bar is { open: number } => {
      return typeof bar === 'object' && bar !== null && 'open' in bar;
    };

    // Validate inputs
    if (!Array.isArray(optionChartData) || !isValidMatchingBar(matchingBar) || typeof breakoutTime !== 'number') {
      console.error('Invalid input data for option metrics calculation');
      return { mfe: 0, closePrice: 0, riskResults: scenarios.map(s => ({ ...s, takeProfitAchieved: false })) };
    }

    const validChartData = optionChartData.filter(isValidBar);

    // Find all bars after the matching bar (breakout time)
    const barsAfterBreakout = validChartData.filter(bar => Number(bar.time) > Number(breakoutTime));

    // Calculate MFE (Maximum Favorable Excursion) - highest price after the open
    let mfe = matchingBar.open; // Start with the open price
    if (barsAfterBreakout.length > 0) {
      const highestHigh = Math.max(...barsAfterBreakout.map(bar => bar.high));
      mfe = Math.max(mfe, highestHigh);
    }

    // Get the close price of the last candle of the day
    const lastBar = validChartData[validChartData.length - 1];
    const closePrice = lastBar ? lastBar.close : matchingBar.open;

    // Calculate take profit prices for all scenarios
    const scenarioCalculations = scenarios.map(scenario => ({
      ...scenario,
      takeProfitPrice: matchingBar.open + scenario.takeProfitOffset
    }));

    // Calculate take profit achieved for each scenario
    const riskResults = scenarioCalculations.map(scenario => {
      let takeProfitAchieved = false;
      let takeProfitTimestamp: string | undefined = undefined;

      // Check each bar after the breakout in chronological order
      for (const bar of barsAfterBreakout) {
        // Check if take profit was hit (price went above take profit)
        if (bar.high >= scenario.takeProfitPrice) {
          takeProfitAchieved = true;
          // Capture the timestamp when take profit was achieved
          takeProfitTimestamp = new Date(Number(bar.time) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          });
          break; // Take profit hit, exit early
        }
      }

      return {
        id: scenario.id,
        takeProfitOffset: scenario.takeProfitOffset,
        takeProfitAchieved: takeProfitAchieved,
        takeProfitTimestamp: takeProfitTimestamp
      };
    });

    console.log('Option metrics calculated:', {
      openPrice: matchingBar.open,
      mfe,
      closePrice,
      scenarioCalculations,
      riskResults,
      barsAfterBreakout: barsAfterBreakout.length
    });

    return {
      mfe,
      closePrice,
      riskResults
    };
  }, []); // Empty dependency array since this function doesn't depend on any props or state







  // Function to perform straddle analysis for first candle entry (for breakeven lines)
  async function performStraddleAnalysisFirstCandle(
    symbol: string,
    date: string,
    strikePrice: number,
    entryBar: { time: Time; open: number }
  ) {
    try {
      console.log('Performing first candle straddle analysis:', { symbol, date, strikePrice, entryBar });

      // Parse date string for option symbol generation
      const [yearFull, monthStr, dayStr] = date.split('-');
      const year = yearFull.slice(-2);
      const month = monthStr;
      const day = dayStr;
      const strikeFormatted = (strikePrice * 1000).toString().padStart(8, '0');

      // Create both call and put option symbols
      const callOptionSymbol = `${symbol}${year}${month}${day}C${strikeFormatted}`;
      const putOptionSymbol = `${symbol}${year}${month}${day}P${strikeFormatted}`;

      console.log('Option symbols:', { callOptionSymbol, putOptionSymbol });

      // Calculate time range for option data (9:30 AM to 4:00 PM ET)
      const entryTime = entryBar.time;
      const startTime = `${date}T09:30:00-04:00`;
      const endTime = `${date}T16:00:00-04:00`;

      console.log('Time range:', { startTime, endTime, entryTime });

      // Fetch both call and put option data (using 1Min candles for precise risk management)
      const [callResponse, putResponse] = await Promise.all([
        fetch(`/api/historical-option-bars?symbol=${callOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`),
        fetch(`/api/historical-option-bars?symbol=${putOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`)
      ]);

      if (!callResponse.ok || !putResponse.ok) {
        throw new Error('Failed to fetch option data for first candle straddle');
      }

      const [callData, putData] = await Promise.all([
        callResponse.json(),
        putResponse.json()
      ]);

      console.log('First candle option data received:', { callData, putData });

      // Transform option data to chart format
      const callChartData = transformAlpacaDataToChart(callData);
      const putChartData = transformAlpacaDataToChart(putData);

      console.log('First candle transformed option data:', { callChartData, putChartData });

      // Find the matching bars at entry time for both call and put
      const matchingCallBar = callChartData.find(bar => Number(bar.time) === Number(entryTime));
      const matchingPutBar = putChartData.find(bar => Number(bar.time) === Number(entryTime));

      if (matchingCallBar && matchingPutBar) {
        console.log('Found matching first candle option bars:', { matchingCallBar, matchingPutBar });

        // Calculate metrics for both options
        const callMetrics = calculateOptionMetrics(callChartData, matchingCallBar, entryTime, riskScenarios);
        const putMetrics = calculateOptionMetrics(putChartData, matchingPutBar, entryTime, riskScenarios);

        // Calculate straddle metrics
        const straddlePurchasePrice = matchingCallBar.open + matchingPutBar.open;
        const straddleClosePrice = callMetrics.closePrice + putMetrics.closePrice;
        const straddleMFE = callMetrics.mfe + putMetrics.mfe;

        // Calculate straddle risk scenarios (analyze combined value)
        const straddleRiskResults = riskScenarios.map(scenario => {
          let takeProfitAchieved = false;
          let takeProfitTimestamp: string | undefined = undefined;

          // Calculate target price for the straddle (purchase price + take profit offset)
          const straddleTargetPrice = straddlePurchasePrice + scenario.takeProfitOffset;

          // Check each time period to see if combined straddle value reached target
          const maxTimeLength = Math.max(callChartData.length, putChartData.length);
          for (let i = 0; i < maxTimeLength; i++) {
            const callBar = callChartData[i];
            const putBar = putChartData[i];

            // Skip if either bar is missing (shouldn't happen but safety check)
            if (!callBar || !putBar) continue;

            // Skip bars before entry time
            if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

            // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
            const combinedStraddleValue = callBar.high + putBar.high;

            // Check if target was reached
            if (combinedStraddleValue >= straddleTargetPrice) {
              takeProfitAchieved = true;
              takeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                timeZone: 'America/New_York',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              });
              break; // Target reached, exit early
            }
          }

          return {
            id: scenario.id,
            takeProfitOffset: scenario.takeProfitOffset,
            takeProfitAchieved: takeProfitAchieved,
            takeProfitTimestamp: takeProfitTimestamp
          };
        });

        // Set the first candle straddle analysis results
        setOptionAnalysisResultsFirstCandle({
          strikePrice: strikePrice,
          callPurchasePrice: matchingCallBar.open,
          putPurchasePrice: matchingPutBar.open,
          straddlePurchasePrice: straddlePurchasePrice,
          breakoutBarTime: new Date(Number(entryTime) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          callMFE: callMetrics.mfe,
          putMFE: putMetrics.mfe,
          straddleMFE: straddleMFE,
          callClosePrice: callMetrics.closePrice,
          putClosePrice: putMetrics.closePrice,
          straddleClosePrice: straddleClosePrice,
          riskScenarios: straddleRiskResults
        });

        // Calculate and draw breakeven lines for first candle straddle
        const breakevenHigh = strikePrice + straddlePurchasePrice;
        const breakevenLow = strikePrice - straddlePurchasePrice;

        console.log('First candle breakeven levels:', { breakevenHigh, breakevenLow, strikePrice, straddlePurchasePrice });

        // Draw breakeven lines on chart
        if (chartRef.current) {
          chartRef.current.addPriceLines(breakevenHigh, breakevenLow);
        }

        // Log breakeven information for reference
        console.log('First candle straddle breakeven lines drawn:', {
          strikePrice,
          straddlePurchasePrice,
          breakevenHigh,
          breakevenLow
        });
      } else {
        console.log('No matching option bars found for first candle entry time:', entryTime);
        setOptionAnalysisResultsFirstCandle(null);
      }
    } catch (error) {
      console.error('Error in first candle straddle analysis:', error);
      setOptionAnalysisResultsFirstCandle(null);
    }
  }
  // Function to perform straddle analysis (both call and put at same strike)
  async function performStraddleAnalysis(
    symbol: string,
    date: string,
    strikePrice: number,
    entryBar: { time: Time; open: number }
  ) {
    try {
      console.log('Performing straddle analysis:', { symbol, date, strikePrice, entryBar });

      // Parse date string for option symbol generation
      const [yearFull, monthStr, dayStr] = date.split('-');
      const year = yearFull.slice(-2);
      const month = monthStr;
      const day = dayStr;
      const strikeFormatted = (strikePrice * 1000).toString().padStart(8, '0');

      // Create both call and put option symbols
      const callOptionSymbol = `${symbol}${year}${month}${day}C${strikeFormatted}`;
      const putOptionSymbol = `${symbol}${year}${month}${day}P${strikeFormatted}`;

      console.log('Generated option symbols:', { callOptionSymbol, putOptionSymbol });

      // Determine timezone offset
      const dateObj = new Date(date);
      const monthNum = dateObj.getMonth() + 1;
      const isEDT = monthNum >= 3 && monthNum <= 10;
      const timezoneOffset = isEDT ? '-04:00' : '-05:00';

      const startTime = encodeURIComponent(`${date}T09:30:00${timezoneOffset}`);
      const endTime = encodeURIComponent(`${date}T16:00:00${timezoneOffset}`);

      // Fetch both call and put option data (using 1Min candles for precise risk management)
      const [callResponse, putResponse] = await Promise.all([
        fetch(`/api/historical-option-bars?symbol=${callOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`),
        fetch(`/api/historical-option-bars?symbol=${putOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`)
      ]);

      if (!callResponse.ok || !putResponse.ok) {
        throw new Error('Failed to fetch option data for straddle');
      }

      const [callData, putData] = await Promise.all([
        callResponse.json(),
        putResponse.json()
      ]);

      console.log('Call option data:', callData);
      console.log('Put option data:', putData);

      if (!callData.bars || callData.bars.length === 0 || !putData.bars || putData.bars.length === 0) {
        console.log('Insufficient option data for straddle analysis');
        setOptionAnalysisResults(null);
        return;
      }

      // Transform option data to chart format
      const callChartData = transformAlpacaDataToChart(callData);
      const putChartData = transformAlpacaDataToChart(putData);

      if (!callChartData || callChartData.length === 0 || !putChartData || putChartData.length === 0) {
        console.log('No valid option chart data after transformation');
        setOptionAnalysisResults(null);
        return;
      }

      // Find matching bars for entry time
      const entryTime = entryBar.time;
      const matchingCallBar = callChartData.find(bar => bar.time === entryTime);
      const matchingPutBar = putChartData.find(bar => bar.time === entryTime);

      if (matchingCallBar && matchingPutBar) {
        console.log('Found matching option bars:', { matchingCallBar, matchingPutBar });

        // Calculate metrics for both options
        const callMetrics = calculateOptionMetrics(callChartData, matchingCallBar, entryTime, riskScenarios);
        const putMetrics = calculateOptionMetrics(putChartData, matchingPutBar, entryTime, riskScenarios);

        // Calculate straddle metrics
        const straddlePurchasePrice = matchingCallBar.open + matchingPutBar.open;
        const straddleClosePrice = callMetrics.closePrice + putMetrics.closePrice;
        const straddleMFE = callMetrics.mfe + putMetrics.mfe;

        // Calculate straddle risk scenarios (analyze combined value)
        const straddleRiskResults = riskScenarios.map(scenario => {
          let takeProfitAchieved = false;
          let takeProfitTimestamp: string | undefined = undefined;

          // Calculate target price for the straddle (purchase price + take profit offset)
          const straddleTargetPrice = straddlePurchasePrice + scenario.takeProfitOffset;

          // Check each time period to see if combined straddle value reached target
          const maxTimeLength = Math.max(callChartData.length, putChartData.length);

          for (let i = 0; i < maxTimeLength; i++) {
            const callBar = callChartData[i];
            const putBar = putChartData[i];

            // Skip if either bar is missing (shouldn't happen but safety check)
            if (!callBar || !putBar) continue;

            // Skip bars before entry time
            if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

            // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
            const combinedStraddleValue = callBar.high + putBar.high;

            // Check if straddle reached take profit target
            if (combinedStraddleValue >= straddleTargetPrice) {
              takeProfitAchieved = true;
              // Capture the timestamp when take profit was achieved
              takeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                timeZone: 'America/New_York',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              });
              break;
            }
          }

          // Calculate slippage results for this scenario
          const slippageResults = slippageScenarios.map(slippageScenario => {
            let slippageTakeProfitAchieved = false;
            let slippageTakeProfitTimestamp: string | undefined = undefined;

            // Calculate target price with slippage (purchase price + slippage + take profit offset)
            const slippageTargetPrice = straddlePurchasePrice + slippageScenario.slippageAmount + scenario.takeProfitOffset;

            // Check each time period to see if combined straddle value reached slippage-adjusted target
            for (let i = 0; i < maxTimeLength; i++) {
              const callBar = callChartData[i];
              const putBar = putChartData[i];

              // Skip if either bar is missing (shouldn't happen but safety check)
              if (!callBar || !putBar) continue;

              // Skip bars before entry time
              if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

              // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
              const combinedStraddleValue = callBar.high + putBar.high;

              // Check if straddle reached slippage-adjusted take profit target
              if (combinedStraddleValue >= slippageTargetPrice) {
                slippageTakeProfitAchieved = true;
                // Capture the timestamp when take profit was achieved
                slippageTakeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                  timeZone: 'America/New_York',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                });
                break;
              }
            }

            return {
              slippageId: slippageScenario.id,
              slippageAmount: slippageScenario.slippageAmount,
              takeProfitAchieved: slippageTakeProfitAchieved,
              takeProfitTimestamp: slippageTakeProfitTimestamp
            };
          });

          return {
            id: scenario.id,
            takeProfitOffset: scenario.takeProfitOffset,
            takeProfitAchieved: takeProfitAchieved,
            takeProfitTimestamp: takeProfitTimestamp,
            slippageResults: slippageResults
          };
        });

        // Set the straddle analysis results
        setOptionAnalysisResults({
          strikePrice: strikePrice,
          callPurchasePrice: matchingCallBar.open,
          putPurchasePrice: matchingPutBar.open,
          straddlePurchasePrice: straddlePurchasePrice,
          breakoutBarTime: new Date(Number(entryTime) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          callMFE: callMetrics.mfe,
          putMFE: putMetrics.mfe,
          straddleMFE: straddleMFE,
          callClosePrice: callMetrics.closePrice,
          putClosePrice: putMetrics.closePrice,
          straddleClosePrice: straddleClosePrice,
          riskScenarios: straddleRiskResults
        });
      } else {
        console.log('No matching option bars found for entry time:', entryTime);
        setOptionAnalysisResults(null);
      }

    } catch (error) {
      console.error('Error in straddle analysis:', error);
      setOptionAnalysisResults(null);
    }
  }

  // Function to perform straddle analysis for 4th candle entry
  async function performStraddleAnalysis4th(
    symbol: string,
    date: string,
    strikePrice: number,
    entryBar: { time: Time; open: number }
  ) {
    try {
      console.log('Performing 4th candle straddle analysis:', { symbol, date, strikePrice, entryBar });

      // Parse date string for option symbol generation
      const [yearFull, monthStr, dayStr] = date.split('-');
      const year = yearFull.slice(-2);
      const month = monthStr;
      const day = dayStr;
      const strikeFormatted = (strikePrice * 1000).toString().padStart(8, '0');

      // Create both call and put option symbols
      const callOptionSymbol = `${symbol}${year}${month}${day}C${strikeFormatted}`;
      const putOptionSymbol = `${symbol}${year}${month}${day}P${strikeFormatted}`;

      console.log('Generated option symbols for 4th candle:', { callOptionSymbol, putOptionSymbol });

      // Determine timezone offset
      const dateObj = new Date(date);
      const monthNum = dateObj.getMonth() + 1;
      const isEDT = monthNum >= 3 && monthNum <= 10;
      const timezoneOffset = isEDT ? '-04:00' : '-05:00';

      const startTime = encodeURIComponent(`${date}T09:30:00${timezoneOffset}`);
      const endTime = encodeURIComponent(`${date}T16:00:00${timezoneOffset}`);

      // Fetch both call and put option data (using 1Min candles for precise risk management)
      const [callResponse, putResponse] = await Promise.all([
        fetch(`/api/historical-option-bars?symbol=${callOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`),
        fetch(`/api/historical-option-bars?symbol=${putOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`)
      ]);

      if (!callResponse.ok || !putResponse.ok) {
        throw new Error('Failed to fetch option data for 4th candle straddle');
      }

      const [callData, putData] = await Promise.all([
        callResponse.json(),
        putResponse.json()
      ]);

      console.log('4th candle Call option data:', callData);
      console.log('4th candle Put option data:', putData);

      if (!callData.bars || callData.bars.length === 0 || !putData.bars || putData.bars.length === 0) {
        console.log('Insufficient option data for 4th candle straddle analysis');
        setOptionAnalysisResults4th(null);
        return;
      }

      // Transform option data to chart format
      const callChartData = transformAlpacaDataToChart(callData);
      const putChartData = transformAlpacaDataToChart(putData);

      console.log('4th candle transformed data:', { callBars: callChartData.length, putBars: putChartData.length });

      // Find matching option bars for the entry time
      const entryTime = entryBar.time;
      const matchingCallBar = callChartData.find(bar => bar.time === entryTime);
      const matchingPutBar = putChartData.find(bar => bar.time === entryTime);

      if (matchingCallBar && matchingPutBar) {
        console.log('Found matching 4th candle option bars:', { matchingCallBar, matchingPutBar });

        // Calculate metrics for both options
        const callMetrics = calculateOptionMetrics(callChartData, matchingCallBar, entryTime, riskScenarios);
        const putMetrics = calculateOptionMetrics(putChartData, matchingPutBar, entryTime, riskScenarios);

        // Calculate straddle metrics
        const straddlePurchasePrice = matchingCallBar.open + matchingPutBar.open;
        const straddleClosePrice = callMetrics.closePrice + putMetrics.closePrice;
        const straddleMFE = callMetrics.mfe + putMetrics.mfe;

        // Calculate straddle risk scenarios (analyze combined value)
        const straddleRiskResults = riskScenarios.map(scenario => {
          let takeProfitAchieved = false;
          let takeProfitTimestamp: string | undefined = undefined;

          // Calculate target price for the straddle (purchase price + take profit offset)
          const straddleTargetPrice = straddlePurchasePrice + scenario.takeProfitOffset;

          // Check each time period to see if combined straddle value reached target
          const maxTimeLength = Math.max(callChartData.length, putChartData.length);

          for (let i = 0; i < maxTimeLength; i++) {
            const callBar = callChartData[i];
            const putBar = putChartData[i];

            // Skip if either bar is missing (shouldn't happen but safety check)
            if (!callBar || !putBar) continue;

            // Skip bars before entry time
            if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

            // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
            const combinedStraddleValue = callBar.high + putBar.high;

            // Check if straddle reached take profit target
            if (combinedStraddleValue >= straddleTargetPrice) {
              takeProfitAchieved = true;
              // Capture the timestamp when take profit was achieved
              takeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                timeZone: 'America/New_York',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              });
              break;
            }
          }

          // Calculate slippage results for this scenario
          const slippageResults = slippageScenarios.map(slippageScenario => {
            let slippageTakeProfitAchieved = false;
            let slippageTakeProfitTimestamp: string | undefined = undefined;

            // Calculate target price with slippage (purchase price + slippage + take profit offset)
            const slippageTargetPrice = straddlePurchasePrice + slippageScenario.slippageAmount + scenario.takeProfitOffset;

            // Check each time period to see if combined straddle value reached slippage-adjusted target
            for (let i = 0; i < maxTimeLength; i++) {
              const callBar = callChartData[i];
              const putBar = putChartData[i];

              // Skip if either bar is missing (shouldn't happen but safety check)
              if (!callBar || !putBar) continue;

              // Skip bars before entry time
              if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

              // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
              const combinedStraddleValue = callBar.high + putBar.high;

              // Check if straddle reached slippage-adjusted take profit target
              if (combinedStraddleValue >= slippageTargetPrice) {
                slippageTakeProfitAchieved = true;
                // Capture the timestamp when take profit was achieved
                slippageTakeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                  timeZone: 'America/New_York',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                });
                break;
              }
            }

            return {
              slippageId: slippageScenario.id,
              slippageAmount: slippageScenario.slippageAmount,
              takeProfitAchieved: slippageTakeProfitAchieved,
              takeProfitTimestamp: slippageTakeProfitTimestamp
            };
          });

          return {
            id: scenario.id,
            takeProfitOffset: scenario.takeProfitOffset,
            takeProfitAchieved: takeProfitAchieved,
            takeProfitTimestamp: takeProfitTimestamp,
            slippageResults: slippageResults
          };
        });

        // Set the 4th candle straddle analysis results
        setOptionAnalysisResults4th({
          strikePrice: strikePrice,
          callPurchasePrice: matchingCallBar.open,
          putPurchasePrice: matchingPutBar.open,
          straddlePurchasePrice: straddlePurchasePrice,
          breakoutBarTime: new Date(Number(entryTime) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          callMFE: callMetrics.mfe,
          putMFE: putMetrics.mfe,
          straddleMFE: straddleMFE,
          callClosePrice: callMetrics.closePrice,
          putClosePrice: putMetrics.closePrice,
          straddleClosePrice: straddleClosePrice,
          riskScenarios: straddleRiskResults
        });
      } else {
        console.log('No matching 4th candle option bars found for entry time:', entryTime);
        setOptionAnalysisResults4th(null);
      }

    } catch (error) {
      console.error('Error in 4th candle straddle analysis:', error);
      setOptionAnalysisResults4th(null);
    }
  }

  // Function to perform straddle analysis for 7th candle entry
  async function performStraddleAnalysis7th(
    symbol: string,
    date: string,
    strikePrice: number,
    entryBar: { time: Time; open: number }
  ) {
    try {
      console.log('Performing 7th candle straddle analysis:', { symbol, date, strikePrice, entryBar });

      // Parse date string for option symbol generation
      const [yearFull, monthStr, dayStr] = date.split('-');
      const year = yearFull.slice(-2);
      const month = monthStr;
      const day = dayStr;
      const strikeFormatted = (strikePrice * 1000).toString().padStart(8, '0');

      // Create both call and put option symbols
      const callOptionSymbol = `${symbol}${year}${month}${day}C${strikeFormatted}`;
      const putOptionSymbol = `${symbol}${year}${month}${day}P${strikeFormatted}`;

      console.log('Generated option symbols for 7th candle:', { callOptionSymbol, putOptionSymbol });

      // Determine timezone offset
      const dateObj = new Date(date);
      const monthNum = dateObj.getMonth() + 1;
      const isEDT = monthNum >= 3 && monthNum <= 10;
      const timezoneOffset = isEDT ? '-04:00' : '-05:00';

      const startTime = encodeURIComponent(`${date}T09:30:00${timezoneOffset}`);
      const endTime = encodeURIComponent(`${date}T16:00:00${timezoneOffset}`);

      // Fetch both call and put option data (using 1Min candles for precise risk management)
      const [callResponse, putResponse] = await Promise.all([
        fetch(`/api/historical-option-bars?symbol=${callOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`),
        fetch(`/api/historical-option-bars?symbol=${putOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`)
      ]);

      if (!callResponse.ok || !putResponse.ok) {
        throw new Error('Failed to fetch option data for 7th candle straddle');
      }

      const [callData, putData] = await Promise.all([
        callResponse.json(),
        putResponse.json()
      ]);

      console.log('7th candle Call option data:', callData);
      console.log('7th candle Put option data:', putData);

      if (!callData.bars || callData.bars.length === 0 || !putData.bars || putData.bars.length === 0) {
        console.log('Insufficient option data for 7th candle straddle analysis');
        setOptionAnalysisResults7th(null);
        return;
      }

      // Transform option data to chart format
      const callChartData = transformAlpacaDataToChart(callData);
      const putChartData = transformAlpacaDataToChart(putData);

      console.log('7th candle transformed data:', { callBars: callChartData.length, putBars: putChartData.length });

      // Find matching option bars for the entry time
      const entryTime = entryBar.time;
      const matchingCallBar = callChartData.find(bar => bar.time === entryTime);
      const matchingPutBar = putChartData.find(bar => bar.time === entryTime);

      if (matchingCallBar && matchingPutBar) {
        console.log('Found matching 7th candle option bars:', { matchingCallBar, matchingPutBar });

        // Calculate metrics for both options
        const callMetrics = calculateOptionMetrics(callChartData, matchingCallBar, entryTime, riskScenarios);
        const putMetrics = calculateOptionMetrics(putChartData, matchingPutBar, entryTime, riskScenarios);

        // Calculate straddle metrics
        const straddlePurchasePrice = matchingCallBar.open + matchingPutBar.open;
        const straddleClosePrice = callMetrics.closePrice + putMetrics.closePrice;
        const straddleMFE = callMetrics.mfe + putMetrics.mfe;

        // Calculate straddle risk scenarios (analyze combined value)
        const straddleRiskResults = riskScenarios.map(scenario => {
          let takeProfitAchieved = false;
          let takeProfitTimestamp: string | undefined = undefined;

          // Calculate target price for the straddle (purchase price + take profit offset)
          const straddleTargetPrice = straddlePurchasePrice + scenario.takeProfitOffset;

          // Check each time period to see if combined straddle value reached target
          const maxTimeLength = Math.max(callChartData.length, putChartData.length);

          for (let i = 0; i < maxTimeLength; i++) {
            const callBar = callChartData[i];
            const putBar = putChartData[i];

            // Skip if either bar is missing (shouldn't happen but safety check)
            if (!callBar || !putBar) continue;

            // Skip bars before entry time
            if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

            // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
            const combinedStraddleValue = callBar.high + putBar.high;

            // Check if straddle reached take profit target
            if (combinedStraddleValue >= straddleTargetPrice) {
              takeProfitAchieved = true;
              // Capture the timestamp when take profit was achieved
              takeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                timeZone: 'America/New_York',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              });
              break;
            }
          }

          // Calculate slippage results for this scenario
          const slippageResults = slippageScenarios.map(slippageScenario => {
            let slippageTakeProfitAchieved = false;
            let slippageTakeProfitTimestamp: string | undefined = undefined;

            // Calculate target price with slippage (purchase price + slippage + take profit offset)
            const slippageTargetPrice = straddlePurchasePrice + slippageScenario.slippageAmount + scenario.takeProfitOffset;

            // Check each time period to see if combined straddle value reached slippage-adjusted target
            for (let i = 0; i < maxTimeLength; i++) {
              const callBar = callChartData[i];
              const putBar = putChartData[i];

              // Skip if either bar is missing (shouldn't happen but safety check)
              if (!callBar || !putBar) continue;

              // Skip bars before entry time
              if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

              // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
              const combinedStraddleValue = callBar.high + putBar.high;

              // Check if straddle reached slippage-adjusted take profit target
              if (combinedStraddleValue >= slippageTargetPrice) {
                slippageTakeProfitAchieved = true;
                // Capture the timestamp when take profit was achieved
                slippageTakeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                  timeZone: 'America/New_York',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                });
                break;
              }
            }

            return {
              slippageId: slippageScenario.id,
              slippageAmount: slippageScenario.slippageAmount,
              takeProfitAchieved: slippageTakeProfitAchieved,
              takeProfitTimestamp: slippageTakeProfitTimestamp
            };
          });

          return {
            id: scenario.id,
            takeProfitOffset: scenario.takeProfitOffset,
            takeProfitAchieved: takeProfitAchieved,
            takeProfitTimestamp: takeProfitTimestamp,
            slippageResults: slippageResults
          };
        });

        // Set the 7th candle straddle analysis results
        setOptionAnalysisResults7th({
          strikePrice: strikePrice,
          callPurchasePrice: matchingCallBar.open,
          putPurchasePrice: matchingPutBar.open,
          straddlePurchasePrice: straddlePurchasePrice,
          breakoutBarTime: new Date(Number(entryTime) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          callMFE: callMetrics.mfe,
          putMFE: putMetrics.mfe,
          straddleMFE: straddleMFE,
          callClosePrice: callMetrics.closePrice,
          putClosePrice: putMetrics.closePrice,
          straddleClosePrice: straddleClosePrice,
          riskScenarios: straddleRiskResults
        });
      } else {
        console.log('No matching 7th candle option bars found for entry time:', entryTime);
        setOptionAnalysisResults7th(null);
      }

    } catch (error) {
      console.error('Error in 7th candle straddle analysis:', error);
      setOptionAnalysisResults7th(null);
    }
  }

  // Function to perform straddle analysis for 13th candle entry
  async function performStraddleAnalysis13th(
    symbol: string,
    date: string,
    strikePrice: number,
    entryBar: { time: Time; open: number }
  ) {
    try {
      console.log('Performing 13th candle straddle analysis:', { symbol, date, strikePrice, entryBar });

      // Parse date string for option symbol generation
      const [yearFull, monthStr, dayStr] = date.split('-');
      const year = yearFull.slice(-2);
      const month = monthStr;
      const day = dayStr;
      const strikeFormatted = (strikePrice * 1000).toString().padStart(8, '0');

      // Create both call and put option symbols
      const callOptionSymbol = `${symbol}${year}${month}${day}C${strikeFormatted}`;
      const putOptionSymbol = `${symbol}${year}${month}${day}P${strikeFormatted}`;

      console.log('Generated option symbols for 13th candle:', { callOptionSymbol, putOptionSymbol });

      // Determine timezone offset
      const dateObj = new Date(date);
      const monthNum = dateObj.getMonth() + 1;
      const isEDT = monthNum >= 3 && monthNum <= 10;
      const timezoneOffset = isEDT ? '-04:00' : '-05:00';

      const startTime = encodeURIComponent(`${date}T09:30:00${timezoneOffset}`);
      const endTime = encodeURIComponent(`${date}T16:00:00${timezoneOffset}`);

      // Fetch both call and put option data (using 1Min candles for precise risk management)
      const [callResponse, putResponse] = await Promise.all([
        fetch(`/api/historical-option-bars?symbol=${callOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`),
        fetch(`/api/historical-option-bars?symbol=${putOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=1Min`)
      ]);

      if (!callResponse.ok || !putResponse.ok) {
        throw new Error('Failed to fetch option data for 13th candle straddle');
      }

      const [callData, putData] = await Promise.all([
        callResponse.json(),
        putResponse.json()
      ]);

      console.log('13th candle Call option data:', callData);
      console.log('13th candle Put option data:', putData);

      if (!callData.bars || callData.bars.length === 0 || !putData.bars || putData.bars.length === 0) {
        console.log('Insufficient option data for 13th candle straddle analysis');
        setOptionAnalysisResults13th(null);
        return;
      }

      // Transform option data to chart format
      const callChartData = transformAlpacaDataToChart(callData);
      const putChartData = transformAlpacaDataToChart(putData);

      console.log('13th candle transformed data:', { callBars: callChartData.length, putBars: putChartData.length });

      // Find matching option bars for the entry time
      const entryTime = entryBar.time;
      const matchingCallBar = callChartData.find(bar => bar.time === entryTime);
      const matchingPutBar = putChartData.find(bar => bar.time === entryTime);

      if (matchingCallBar && matchingPutBar) {
        console.log('Found matching 13th candle option bars:', { matchingCallBar, matchingPutBar });

        // Calculate metrics for both options
        const callMetrics = calculateOptionMetrics(callChartData, matchingCallBar, entryTime, riskScenarios);
        const putMetrics = calculateOptionMetrics(putChartData, matchingPutBar, entryTime, riskScenarios);

        // Calculate straddle metrics
        const straddlePurchasePrice = matchingCallBar.open + matchingPutBar.open;
        const straddleClosePrice = callMetrics.closePrice + putMetrics.closePrice;
        const straddleMFE = callMetrics.mfe + putMetrics.mfe;

        // Calculate straddle risk scenarios (analyze combined value)
        const straddleRiskResults = riskScenarios.map(scenario => {
          let takeProfitAchieved = false;
          let takeProfitTimestamp: string | undefined = undefined;

          // Calculate target price for the straddle (purchase price + take profit offset)
          const straddleTargetPrice = straddlePurchasePrice + scenario.takeProfitOffset;

          // Check each time period to see if combined straddle value reached target
          const maxTimeLength = Math.max(callChartData.length, putChartData.length);

          for (let i = 0; i < maxTimeLength; i++) {
            const callBar = callChartData[i];
            const putBar = putChartData[i];

            // Skip if either bar is missing (shouldn't happen but safety check)
            if (!callBar || !putBar) continue;

            // Skip bars before entry time
            if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

            // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
            const combinedStraddleValue = callBar.high + putBar.high;

            // Check if straddle reached take profit target
            if (combinedStraddleValue >= straddleTargetPrice) {
              takeProfitAchieved = true;
              // Capture the timestamp when take profit was achieved
              takeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                timeZone: 'America/New_York',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              });
              break;
            }
          }

          // Calculate slippage results for this scenario
          const slippageResults = slippageScenarios.map(slippageScenario => {
            let slippageTakeProfitAchieved = false;
            let slippageTakeProfitTimestamp: string | undefined = undefined;

            // Calculate target price with slippage (purchase price + slippage + take profit offset)
            const slippageTargetPrice = straddlePurchasePrice + slippageScenario.slippageAmount + scenario.takeProfitOffset;

            // Check each time period to see if combined straddle value reached slippage-adjusted target
            for (let i = 0; i < maxTimeLength; i++) {
              const callBar = callChartData[i];
              const putBar = putChartData[i];

              // Skip if either bar is missing (shouldn't happen but safety check)
              if (!callBar || !putBar) continue;

              // Skip bars before entry time
              if (Number(callBar.time) < Number(entryTime) || Number(putBar.time) < Number(entryTime)) continue;

              // Calculate combined straddle value at this time (using high prices for maximum favorable excursion)
              const combinedStraddleValue = callBar.high + putBar.high;

              // Check if straddle reached slippage-adjusted take profit target
              if (combinedStraddleValue >= slippageTargetPrice) {
                slippageTakeProfitAchieved = true;
                // Capture the timestamp when take profit was achieved
                slippageTakeProfitTimestamp = new Date(Number(callBar.time) * 1000).toLocaleTimeString('en-US', {
                  timeZone: 'America/New_York',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                });
                break;
              }
            }

            return {
              slippageId: slippageScenario.id,
              slippageAmount: slippageScenario.slippageAmount,
              takeProfitAchieved: slippageTakeProfitAchieved,
              takeProfitTimestamp: slippageTakeProfitTimestamp
            };
          });

          return {
            id: scenario.id,
            takeProfitOffset: scenario.takeProfitOffset,
            takeProfitAchieved: takeProfitAchieved,
            takeProfitTimestamp: takeProfitTimestamp,
            slippageResults: slippageResults
          };
        });

        // Set the 13th candle straddle analysis results
        setOptionAnalysisResults13th({
          strikePrice: strikePrice,
          callPurchasePrice: matchingCallBar.open,
          putPurchasePrice: matchingPutBar.open,
          straddlePurchasePrice: straddlePurchasePrice,
          breakoutBarTime: new Date(Number(entryTime) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          callMFE: callMetrics.mfe,
          putMFE: putMetrics.mfe,
          straddleMFE: straddleMFE,
          callClosePrice: callMetrics.closePrice,
          putClosePrice: putMetrics.closePrice,
          straddleClosePrice: straddleClosePrice,
          riskScenarios: straddleRiskResults
        });
      } else {
        console.log('No matching 13th candle option bars found for entry time:', entryTime);
        setOptionAnalysisResults13th(null);
      }

    } catch (error) {
      console.error('Error in 13th candle straddle analysis:', error);
      setOptionAnalysisResults13th(null);
    }
  }

  async function handleFetch() {
    try {
      setIsLoading(true);
      setError(null);
      setOptionAnalysisResults(null);
      setOptionAnalysisResults4th(null);
      setOptionAnalysisResults7th(null);
      setOptionAnalysisResults13th(null);
      setOptionAnalysisResultsFirstCandle(null);

      // Clear existing chart data and price lines first
      if (chartRef.current) {
        chartRef.current.clearData();
        chartRef.current.clearPriceLines();
      }

      let alpacaData;

      if (formType === 'options') {
        alpacaData = await fetchHistoricalOptionBars(formatted, startDate, endDate, timeframe);
      } else {
        // For stocks, use the stock date for both start and end
        alpacaData = await fetchHistoricalStockBars(stockSymbol, stockDate, stockDate, '5Min');
      }

      console.log('Raw Alpaca data:', alpacaData);

      // Transform the Alpaca data into chart-compatible format
      const chartData = transformAlpacaDataToChart(alpacaData);
      console.log('Transformed chart data:', chartData);

      // Validate the data before updating the chart
      if (validateChartData(chartData)) {
        // Update the chart with the new data
        if (chartRef.current) {
          chartRef.current.updateData(chartData);

          // For stock data (5min charts), implement straddle strategy
          if (formType === 'stocks' && chartData.length >= 2) {
            const firstCandle = chartData[0];
            const secondCandle = chartData[1];
            const fourthCandle = chartData.length >= 4 ? chartData[3] : null;
            const seventhCandle = chartData.length >= 7 ? chartData[6] : null;
            const thirteenthCandle = chartData.length >= 13 ? chartData[12] : null;

            console.log('First candle:', firstCandle);
            console.log('Second candle:', secondCandle);
            console.log('Fourth candle:', fourthCandle);
            console.log('Seventh candle:', seventhCandle);
            console.log('Thirteenth candle:', thirteenthCandle);

            // Purchase straddle on open of second candle
            // Strike price: round to nearest whole dollar
            const currentPrice = secondCandle.open;
            const strikePrice = Math.round(currentPrice); // Nearest whole dollar for straddle

            console.log('Buying straddle - Current price:', currentPrice, 'Strike:', strikePrice);

            // Perform first candle straddle analysis for breakeven lines
            const firstCandlePrice = firstCandle.open;
            const firstCandleStrikePrice = Math.round(firstCandlePrice);
            console.log('Buying first candle straddle for breakeven - Current price:', firstCandlePrice, 'Strike:', firstCandleStrikePrice);
            await performStraddleAnalysisFirstCandle(stockSymbol, stockDate, firstCandleStrikePrice, firstCandle);

            // Perform straddle analysis (both call and put at same strike)
            await performStraddleAnalysis(stockSymbol, stockDate, strikePrice, secondCandle);

            // Perform 4th candle straddle analysis if available
            if (fourthCandle) {
              const fourthCandlePrice = fourthCandle.open;
              const fourthStrikePrice = Math.round(fourthCandlePrice);
              console.log('Buying 4th candle straddle - Current price:', fourthCandlePrice, 'Strike:', fourthStrikePrice);
              await performStraddleAnalysis4th(stockSymbol, stockDate, fourthStrikePrice, fourthCandle);
            }

            // Perform 7th candle straddle analysis if available
            if (seventhCandle) {
              const seventhCandlePrice = seventhCandle.open;
              const seventhStrikePrice = Math.round(seventhCandlePrice);
              console.log('Buying 7th candle straddle - Current price:', seventhCandlePrice, 'Strike:', seventhStrikePrice);
              await performStraddleAnalysis7th(stockSymbol, stockDate, seventhStrikePrice, seventhCandle);
            }

            // Perform 13th candle straddle analysis if available
            if (thirteenthCandle) {
              const thirteenthCandlePrice = thirteenthCandle.open;
              const thirteenthStrikePrice = Math.round(thirteenthCandlePrice);
              console.log('Buying 13th candle straddle - Current price:', thirteenthCandlePrice, 'Strike:', thirteenthStrikePrice);
              await performStraddleAnalysis13th(stockSymbol, stockDate, thirteenthStrikePrice, thirteenthCandle);
            }

            // Clear individual option results since we're now using straddle
            // setPutOptionAnalysisResults(null); // Removed - not used in straddle strategy
          }
        }
        setHasDataBeenFetched(true);
      } else {
        throw new Error('Invalid chart data received - no valid data points found');
      }
    } catch (error) {
      console.error('Fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch chart data';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }


  return (
    <main className="h-screen w-full bg-gray-900 flex flex-col lg:flex-row overflow-hidden">
      {/* TradingView Chart - Top on mobile, Left 3/4 on desktop */}
      <div className={`flex-1 ${isFormMinimized ? 'h-full' : 'h-64 sm:h-80 md:h-96'} lg:h-full lg:w-3/4 relative transition-all duration-300 ease-in-out`}>
        <TradingViewChart ref={chartRef} className="w-full h-full" />

        {/* Chart Overlay Messages */}
        {!hasDataBeenFetched && !isLoading && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6">
              <div className="text-4xl mb-4">📊</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2">Fill Out Form to Fetch Chart</h2>
              <p className="text-gray-300 text-sm sm:text-base">
                Enter option details in the form and click &quot;Fetch Historical Bars&quot; to view chart data
              </p>
            </div>
          </div>
        )}

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6">
              <div className="animate-spin text-4xl mb-4">⏳</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2">Loading Chart Data...</h2>
              <p className="text-gray-300 text-sm sm:text-base">
                Fetching historical option bars from Alpaca
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6 max-w-md mx-auto">
              <div className="text-4xl mb-4">❌</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2 text-red-400">Error Loading Chart</h2>
              <p className="text-gray-300 text-sm sm:text-base mb-4">
                {error}
              </p>
              <button
                type="button"
                onClick={() => setError(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm"
              >
                Dismiss
              </button>
            </div>
          </div>
        )}

        {/* Mobile Toggle Button - Only visible on mobile when form is minimized */}
        {isFormMinimized && (
          <button
            type="button"
            onClick={() => {
              setIsFormMinimized(false);
              // Trigger resize after a short delay to allow DOM update
              setTimeout(() => {
                if (chartRef.current) {
                  chartRef.current.resize();
                }
              }, 100);
            }}
            className="absolute bottom-4 right-4 lg:hidden bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 hover:scale-110 transition-all duration-200 z-10 animate-in fade-in zoom-in duration-300"
            aria-label="Show form"
            title="Show option form"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
          </button>
        )}
      </div>

      {/* Thin separator border - horizontal on mobile, vertical on desktop */}
      {!isFormMinimized && <div className="h-px lg:h-full lg:w-px bg-gray-600"></div>}

      {/* Alpaca Option Symbol Generator Form - Bottom on mobile, Right 1/4 on desktop */}
      {!isFormMinimized && (
        <div className="w-full lg:w-1/4 h-auto lg:h-full bg-white overflow-y-auto animate-in slide-in-from-bottom duration-300 lg:animate-none">
          <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 relative">
            {/* Mobile Minimize Button - Only visible on mobile */}
            <button
              type="button"
              onClick={() => {
                setIsFormMinimized(true);
                // Trigger resize after a short delay to allow DOM update
                setTimeout(() => {
                  if (chartRef.current) {
                    chartRef.current.resize();
                  }
                }, 100);
              }}
              className="absolute top-2 right-2 lg:hidden bg-gray-200 text-gray-600 p-2 rounded-full hover:bg-gray-300 hover:scale-110 transition-all duration-200 animate-pulse"
              aria-label="Minimize form"
              title="Minimize form to view chart fullscreen"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </button>
          <h1 className="text-xl sm:text-2xl font-bold text-center text-gray-800">
            {formType === 'options' ? '🧮 Option Historical Charts' : '📊 5min Historical Charts'}
          </h1>

          {/* Form Toggle Switch */}
          <div className="flex items-center justify-center space-x-4 py-2">
            <span className={`text-sm font-medium ${formType === 'options' ? 'text-blue-600' : 'text-gray-500'}`}>
              Options
            </span>
            <button
              type="button"
              onClick={() => {
                setFormType(formType === 'options' ? 'stocks' : 'options');
                setError(null);
                setHasDataBeenFetched(false);
                setOptionAnalysisResults(null);
                setOptionAnalysisResults4th(null);
                setOptionAnalysisResults7th(null);
                setOptionAnalysisResults13th(null);
                setOptionAnalysisResultsFirstCandle(null);
                if (chartRef.current) {
                  chartRef.current.clearData();
                  chartRef.current.clearPriceLines();
                }
              }}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                formType === 'stocks' ? 'bg-blue-600' : 'bg-gray-200'
              }`}
              aria-label={`Switch to ${formType === 'options' ? 'stocks' : 'options'} form`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  formType === 'stocks' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${formType === 'stocks' ? 'text-blue-600' : 'text-gray-500'}`}>
              Stocks
            </span>
          </div>

          <div className="space-y-3 sm:space-y-4">
            {formType === 'options' ? (
              <>
                {/* Symbol */}
                <div>
                  <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-1">
                    Underlying Symbol
                  </label>
                  <AutosuggestInput
                    id="symbol"
                    value={symbol}
                    onChange={setSymbol}
                    suggestions={tickersData}
                    placeholder="e.g. AAPL"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                    maxSuggestions={8}
                  />
                </div>

            {/* Expiration */}
            <div>
              <label htmlFor="expiration" className="block text-sm font-medium text-gray-700 mb-1">
                Expiration Date
              </label>
              <input
                id="expiration"
                type="date"
                value={expiration}
                onChange={(e) => setExpiration(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
              />
            </div>

            {/* Option Type */}
            <div>
              <label htmlFor="option-type" className="block text-sm font-medium text-gray-700 mb-1">
                Option Type
              </label>
              <select
                id="option-type"
                value={type}
                onChange={(e) => setType(e.target.value as 'C' | 'P')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base bg-white"
              >
                <option value="C">Call</option>
                <option value="P">Put</option>
              </select>
            </div>

            {/* Strike Price */}
            <div>
              <label htmlFor="strike" className="block text-sm font-medium text-gray-700 mb-1">
                Strike Price
              </label>
              <input
                id="strike"
                type="number"
                step="0.01"
                value={strike}
                onChange={(e) => setStrike(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                placeholder="e.g. 150.00"
              />
            </div>

            {/* Timeframe */}
            <div>
              <label htmlFor="timeframe" className="block text-sm font-medium text-gray-700 mb-1">
                Timeframe
              </label>
              <select
                id="timeframe"
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base bg-white"
              >
                <option value="1Min">1 Minute</option>
                <option value="5Min">5 Minutes</option>
                <option value="15Min">15 Minutes</option>
                <option value="30Min">30 Minutes</option>
                <option value="1Hour">1 Hour</option>
                <option value="4Hour">4 Hour</option>
                <option value="1Day">1 Day</option>
                <option value="1Week">1 Week</option>
                <option value="1Month">1 Month</option>
              </select>
            </div>

            {/* Chart Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Chart Date Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label htmlFor="start-date" className="block text-xs text-gray-500 mb-1">
                    Start Date
                  </label>
                  <input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => {
                      setStartDate(e.target.value);
                      // Mark that user has manually changed the date range
                      setHasUserChangedDateRange(true);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>
                <div>
                  <label htmlFor="end-date" className="block text-xs text-gray-500 mb-1">
                    End Date
                  </label>
                  <input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => {
                      setEndDate(e.target.value);
                      // Mark that user has manually changed the date range
                      setHasUserChangedDateRange(true);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>
              </div>
            </div>
              </>
            ) : (
              <>
                {/* Stock Symbol */}
                <div>
                  <label htmlFor="stock-symbol" className="block text-sm font-medium text-gray-700 mb-1">
                    Symbol
                  </label>
                  <AutosuggestInput
                    id="stock-symbol"
                    value={stockSymbol}
                    onChange={setStockSymbol}
                    suggestions={tickersData}
                    placeholder="e.g. SPY"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                    maxSuggestions={8}
                  />
                </div>

                {/* Date */}
                <div>
                  <label htmlFor="stock-date" className="block text-sm font-medium text-gray-700 mb-1">
                    Date
                  </label>
                  <input
                    id="stock-date"
                    type="date"
                    value={stockDate}
                    onChange={(e) => setStockDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>

                {/* Dynamic Risk Management Scenarios */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium text-gray-700">Risk Management Scenarios</h3>
                    <button
                      type="button"
                      onClick={addRiskScenario}
                      className="flex items-center gap-1 px-2 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                    >
                      <span className="text-sm font-bold">+</span>
                      Add Scenario
                    </button>
                  </div>

                  {riskScenarios.map((scenario, index) => (
                    <div key={scenario.id} className="relative">
                      <div className="w-full">
                        <label htmlFor={`take-profit-${scenario.id}`} className="block text-xs font-medium text-gray-600 mb-1">
                          Scenario {index + 1}: Take Profit (+$)
                        </label>
                        <input
                          id={`take-profit-${scenario.id}`}
                          type="number"
                          step="0.01"
                          min="0.01"
                          value={scenario.takeProfitOffset}
                          onChange={(e) => updateRiskScenario(scenario.id, 'takeProfitOffset', parseFloat(e.target.value) || 0.25)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                        />
                      </div>
                      {riskScenarios.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeRiskScenario(scenario.id)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors flex items-center justify-center"
                          title="Remove scenario"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                {/* Dynamic Slippage Scenarios */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium text-gray-700">Slippage Scenarios</h3>
                    <button
                      type="button"
                      onClick={addSlippageScenario}
                      className="flex items-center gap-1 px-2 py-1 text-xs bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
                    >
                      <span className="text-sm font-bold">+</span>
                      Add Slippage
                    </button>
                  </div>

                  {slippageScenarios.map((scenario, index) => (
                    <div key={scenario.id} className="relative">
                      <div className="w-full">
                        <label htmlFor={`slippage-${scenario.id}`} className="block text-xs font-medium text-gray-600 mb-1">
                          Slippage {index + 1}: Amount (+$)
                        </label>
                        <input
                          id={`slippage-${scenario.id}`}
                          type="number"
                          step="0.01"
                          min="0.00"
                          value={scenario.slippageAmount}
                          onChange={(e) => updateSlippageScenario(scenario.id, parseFloat(e.target.value) || 0.00)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-base"
                        />
                      </div>
                      {slippageScenarios.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeSlippageScenario(scenario.id)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors flex items-center justify-center"
                          title="Remove slippage scenario"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>

          {formType === 'options' && (
            <div className="pt-3 sm:pt-4 border-t border-gray-200">
              <h2 className="text-sm font-semibold text-gray-600 mb-2">Generated Symbol:</h2>
              <code className="block text-sm sm:text-base bg-gray-100 p-2 sm:p-3 rounded-md text-blue-600 font-mono break-all">
                {formatted || '(fill out all fields)'}
              </code>
            </div>
          )}
          <button
            type="button"
            onClick={handleFetch}
            className="w-full px-4 py-3 bg-blue-600 text-white text-base font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              formType === 'options'
                ? (!formatted || isLoading)
                : (!stockSymbol || !stockDate || isLoading)
            }
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <span className="animate-spin mr-2">⏳</span>
                Loading...
              </span>
            ) : (
              formType === 'options' ? 'Fetch Historical Bars' : 'Fetch 5min Chart'
            )}
          </button>

          {/* Straddle Analysis Results */}
          {formType === 'stocks' && optionAnalysisResults && (
            <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h3 className="text-sm font-semibold text-purple-800 mb-2">Straddle Analysis Results</h3>
              <div className="space-y-1 text-sm text-purple-700">
                <div>
                  <span className="font-medium">Strike Price:</span> ${optionAnalysisResults.strikePrice}
                </div>
                <div>
                  <span className="font-medium">Call Purchase Price:</span> ${optionAnalysisResults.callPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Purchase Price:</span> ${optionAnalysisResults.putPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Purchase Price:</span> ${optionAnalysisResults.straddlePurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call MFE:</span> ${optionAnalysisResults.callMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put MFE:</span> ${optionAnalysisResults.putMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle MFE:</span> ${optionAnalysisResults.straddleMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call Close Price:</span> ${optionAnalysisResults.callClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Close Price:</span> ${optionAnalysisResults.putClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Close Price:</span> ${optionAnalysisResults.straddleClosePrice.toFixed(2)}
                </div>

                {/* Dynamic Risk Management Results */}
                <div className="mt-3 space-y-2">
                  <h4 className="text-xs font-semibold text-purple-800">Risk Management Results</h4>

                  {optionAnalysisResults.riskScenarios.map((scenario, index) => (
                    <div key={scenario.id} className="space-y-1">
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">
                          Scenario {index + 1} (+${scenario.takeProfitOffset.toFixed(2)}):
                        </span>
                        <span className={`font-semibold ${scenario.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                          {scenario.takeProfitAchieved
                            ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                            : `-$${(optionAnalysisResults.straddlePurchasePrice - optionAnalysisResults.straddleClosePrice).toFixed(2)}`
                          }
                        </span>
                      </div>
                      {scenario.takeProfitAchieved && scenario.takeProfitTimestamp && (
                        <div className="text-xs text-green-600 ml-2">
                          ✓ Triggered at {scenario.takeProfitTimestamp} EST
                        </div>
                      )}

                      {/* Slippage Results */}
                      {scenario.slippageResults && scenario.slippageResults.length > 0 && (
                        <div className="ml-4 mt-2 space-y-1">
                          <div className="text-xs font-medium text-gray-500">Slippage Results:</div>
                          {scenario.slippageResults.map((slippageResult) => (
                            <div key={slippageResult.slippageId} className="flex justify-between items-center text-xs">
                              <span className="text-gray-500">
                                +${slippageResult.slippageAmount.toFixed(2)} slippage:
                              </span>
                              <span className={`font-semibold ${slippageResult.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                                {slippageResult.takeProfitAchieved
                                  ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                                  : `-$${((optionAnalysisResults.straddlePurchasePrice + slippageResult.slippageAmount) - optionAnalysisResults.straddleClosePrice).toFixed(2)}`
                                }
                              </span>
                            </div>
                          ))}
                          {/* Separator line after slippage results */}
                          <div className="border-b border-gray-200 mt-2"></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="text-xs text-purple-600 mt-2">
                  Straddle purchased on open of second 5min candle at {optionAnalysisResults.breakoutBarTime} EST
                </div>
              </div>
            </div>
          )}

          {/* 4th Candle Straddle Analysis Results */}
          {formType === 'stocks' && optionAnalysisResults4th && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-sm font-semibold text-blue-800 mb-2">4th Candle Straddle Analysis Results</h3>
              <div className="space-y-1 text-sm text-blue-700">
                <div>
                  <span className="font-medium">Strike Price:</span> ${optionAnalysisResults4th.strikePrice}
                </div>
                <div>
                  <span className="font-medium">Call Purchase Price:</span> ${optionAnalysisResults4th.callPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Purchase Price:</span> ${optionAnalysisResults4th.putPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Purchase Price:</span> ${optionAnalysisResults4th.straddlePurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call MFE:</span> ${optionAnalysisResults4th.callMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put MFE:</span> ${optionAnalysisResults4th.putMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle MFE:</span> ${optionAnalysisResults4th.straddleMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call Close Price:</span> ${optionAnalysisResults4th.callClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Close Price:</span> ${optionAnalysisResults4th.putClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Close Price:</span> ${optionAnalysisResults4th.straddleClosePrice.toFixed(2)}
                </div>

                {/* Dynamic Risk Management Results */}
                <div className="mt-3 space-y-2">
                  <h4 className="text-xs font-semibold text-blue-800">Risk Management Results</h4>

                  {optionAnalysisResults4th.riskScenarios.map((scenario, index) => (
                    <div key={scenario.id} className="space-y-1">
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">
                          Scenario {index + 1} (+${scenario.takeProfitOffset.toFixed(2)}):
                        </span>
                        <span className={`font-semibold ${scenario.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                          {scenario.takeProfitAchieved
                            ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                            : `-$${(optionAnalysisResults4th.straddlePurchasePrice - optionAnalysisResults4th.straddleClosePrice).toFixed(2)}`
                          }
                        </span>
                      </div>
                      {scenario.takeProfitAchieved && scenario.takeProfitTimestamp && (
                        <div className="text-xs text-green-600 ml-2">
                          ✓ Triggered at {scenario.takeProfitTimestamp} EST
                        </div>
                      )}

                      {/* Slippage Results */}
                      {scenario.slippageResults && scenario.slippageResults.length > 0 && (
                        <div className="ml-4 mt-2 space-y-1">
                          <div className="text-xs font-medium text-gray-500">Slippage Results:</div>
                          {scenario.slippageResults.map((slippageResult) => (
                            <div key={slippageResult.slippageId} className="flex justify-between items-center text-xs">
                              <span className="text-gray-500">
                                +${slippageResult.slippageAmount.toFixed(2)} slippage:
                              </span>
                              <span className={`font-semibold ${slippageResult.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                                {slippageResult.takeProfitAchieved
                                  ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                                  : `-$${((optionAnalysisResults4th.straddlePurchasePrice + slippageResult.slippageAmount) - optionAnalysisResults4th.straddleClosePrice).toFixed(2)}`
                                }
                              </span>
                            </div>
                          ))}
                          {/* Separator line after slippage results */}
                          <div className="border-b border-gray-200 mt-2"></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="text-xs text-blue-600 mt-2">
                  Straddle purchased on open of fourth 5min candle at {optionAnalysisResults4th.breakoutBarTime} EST
                </div>
              </div>
            </div>
          )}

          {/* 7th Candle Straddle Analysis Results */}
          {formType === 'stocks' && optionAnalysisResults7th && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="text-sm font-semibold text-green-800 mb-2">7th Candle Straddle Analysis Results</h3>
              <div className="space-y-1 text-sm text-green-700">
                <div>
                  <span className="font-medium">Strike Price:</span> ${optionAnalysisResults7th.strikePrice}
                </div>
                <div>
                  <span className="font-medium">Call Purchase Price:</span> ${optionAnalysisResults7th.callPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Purchase Price:</span> ${optionAnalysisResults7th.putPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Purchase Price:</span> ${optionAnalysisResults7th.straddlePurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call MFE:</span> ${optionAnalysisResults7th.callMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put MFE:</span> ${optionAnalysisResults7th.putMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle MFE:</span> ${optionAnalysisResults7th.straddleMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call Close Price:</span> ${optionAnalysisResults7th.callClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Close Price:</span> ${optionAnalysisResults7th.putClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Close Price:</span> ${optionAnalysisResults7th.straddleClosePrice.toFixed(2)}
                </div>

                {/* Dynamic Risk Management Results */}
                <div className="mt-3 space-y-2">
                  <h4 className="text-xs font-semibold text-green-800">Risk Management Results</h4>

                  {optionAnalysisResults7th.riskScenarios.map((scenario, index) => (
                    <div key={scenario.id} className="space-y-1">
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">
                          Scenario {index + 1} (+${scenario.takeProfitOffset.toFixed(2)}):
                        </span>
                        <span className={`font-semibold ${scenario.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                          {scenario.takeProfitAchieved
                            ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                            : `-$${(optionAnalysisResults7th.straddlePurchasePrice - optionAnalysisResults7th.straddleClosePrice).toFixed(2)}`
                          }
                        </span>
                      </div>
                      {scenario.takeProfitAchieved && scenario.takeProfitTimestamp && (
                        <div className="text-xs text-green-600 ml-2">
                          ✓ Triggered at {scenario.takeProfitTimestamp} EST
                        </div>
                      )}

                      {/* Slippage Results */}
                      {scenario.slippageResults && scenario.slippageResults.length > 0 && (
                        <div className="ml-4 mt-2 space-y-1">
                          <div className="text-xs font-medium text-gray-500">Slippage Results:</div>
                          {scenario.slippageResults.map((slippageResult) => (
                            <div key={slippageResult.slippageId} className="flex justify-between items-center text-xs">
                              <span className="text-gray-500">
                                +${slippageResult.slippageAmount.toFixed(2)} slippage:
                              </span>
                              <span className={`font-semibold ${slippageResult.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                                {slippageResult.takeProfitAchieved
                                  ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                                  : `-$${((optionAnalysisResults7th.straddlePurchasePrice + slippageResult.slippageAmount) - optionAnalysisResults7th.straddleClosePrice).toFixed(2)}`
                                }
                              </span>
                            </div>
                          ))}
                          {/* Separator line after slippage results */}
                          <div className="border-b border-gray-200 mt-2"></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="text-xs text-green-600 mt-2">
                  Straddle purchased on open of seventh 5min candle at {optionAnalysisResults7th.breakoutBarTime} EST
                </div>
              </div>
            </div>
          )}

          {/* 13th Candle Straddle Analysis Results */}
          {formType === 'stocks' && optionAnalysisResults13th && (
            <div className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h3 className="text-sm font-semibold text-orange-800 mb-2">13th Candle Straddle Analysis Results</h3>
              <div className="space-y-1 text-sm text-orange-700">
                <div>
                  <span className="font-medium">Strike Price:</span> ${optionAnalysisResults13th.strikePrice}
                </div>
                <div>
                  <span className="font-medium">Call Purchase Price:</span> ${optionAnalysisResults13th.callPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Purchase Price:</span> ${optionAnalysisResults13th.putPurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Purchase Price:</span> ${optionAnalysisResults13th.straddlePurchasePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call MFE:</span> ${optionAnalysisResults13th.callMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put MFE:</span> ${optionAnalysisResults13th.putMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle MFE:</span> ${optionAnalysisResults13th.straddleMFE.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Call Close Price:</span> ${optionAnalysisResults13th.callClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Put Close Price:</span> ${optionAnalysisResults13th.putClosePrice.toFixed(2)}
                </div>
                <div>
                  <span className="font-medium">Straddle Close Price:</span> ${optionAnalysisResults13th.straddleClosePrice.toFixed(2)}
                </div>

                {/* Dynamic Risk Management Results */}
                <div className="mt-3 space-y-2">
                  <h4 className="text-xs font-semibold text-orange-800">Risk Management Results</h4>

                  {optionAnalysisResults13th.riskScenarios.map((scenario, index) => (
                    <div key={scenario.id} className="space-y-1">
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">
                          Scenario {index + 1} (+${scenario.takeProfitOffset.toFixed(2)}):
                        </span>
                        <span className={`font-semibold ${scenario.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                          {scenario.takeProfitAchieved
                            ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                            : `-$${(optionAnalysisResults13th.straddlePurchasePrice - optionAnalysisResults13th.straddleClosePrice).toFixed(2)}`
                          }
                        </span>
                      </div>
                      {scenario.takeProfitAchieved && scenario.takeProfitTimestamp && (
                        <div className="text-xs text-green-600 ml-2">
                          ✓ Triggered at {scenario.takeProfitTimestamp} EST
                        </div>
                      )}

                      {/* Slippage Results */}
                      {scenario.slippageResults && scenario.slippageResults.length > 0 && (
                        <div className="ml-4 mt-2 space-y-1">
                          <div className="text-xs font-medium text-gray-500">Slippage Results:</div>
                          {scenario.slippageResults.map((slippageResult) => (
                            <div key={slippageResult.slippageId} className="flex justify-between items-center text-xs">
                              <span className="text-gray-500">
                                +${slippageResult.slippageAmount.toFixed(2)} slippage:
                              </span>
                              <span className={`font-semibold ${slippageResult.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                                {slippageResult.takeProfitAchieved
                                  ? `+$${scenario.takeProfitOffset.toFixed(2)}`
                                  : `-$${((optionAnalysisResults13th.straddlePurchasePrice + slippageResult.slippageAmount) - optionAnalysisResults13th.straddleClosePrice).toFixed(2)}`
                                }
                              </span>
                            </div>
                          ))}
                          {/* Separator line after slippage results */}
                          <div className="border-b border-gray-200 mt-2"></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="text-xs text-orange-600 mt-2">
                  Straddle purchased on open of thirteenth 5min candle at {optionAnalysisResults13th.breakoutBarTime} EST
                </div>
              </div>
            </div>
          )}


        </div>
        </div>
      )}
    </main>
  );
}
