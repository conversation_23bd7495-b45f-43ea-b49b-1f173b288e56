// src/utils/fetchHistoricalStockBars.ts

export async function fetchHistoricalStockBars(symbol: string, startDate: string, endDate: string, timeframe: string) {
  // Determine the correct timezone offset for the given date
  // EST is -05:00 (Nov-Mar), EDT is -04:00 (Mar-Nov)
  const date = new Date(startDate);

  // Daylight saving time typically runs from second Sunday in March to first Sunday in November
  // For simplicity, we'll use a rough approximation: March-October is EDT, Nov-Feb is EST
  const month = date.getMonth() + 1; // getMonth() returns 0-11, we want 1-12
  const isEDT = month >= 3 && month <= 10; // March through October
  const timezoneOffset = isEDT ? '-04:00' : '-05:00';

  console.log(`Date: ${startDate}, Month: ${month}, Using timezone: ${timezoneOffset}`);

  const start = `${startDate}T09:30:00${timezoneOffset}`;
  const end = `${endDate}T16:00:00${timezoneOffset}`;
  const params = new URLSearchParams({ symbol, start, end, timeframe });

  const res = await fetch(`/api/historical-stock-bars?${params.toString()}`);

  if (!res.ok) {
    const error = await res.json();
    throw new Error(error.error || 'Failed to fetch historical stock bars');
  }

  return await res.json();
}
